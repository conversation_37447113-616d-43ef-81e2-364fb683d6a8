// Initialize Stripe (replace with your actual publishable key)
const stripe = Stripe('pk_test_51234567890abcdef'); // Replace with your actual Stripe publishable key

// DOM Elements
const quoteModal = document.getElementById('quoteModal');
const quoteForm = document.getElementById('quoteForm');
const serviceTypeInput = document.getElementById('serviceType');
const closeModal = document.querySelector('.close');
const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
const nav = document.querySelector('.nav');

// Mobile Menu Toggle
mobileMenuToggle.addEventListener('click', () => {
    nav.classList.toggle('active');
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Buy Now Button Functionality
document.querySelectorAll('.buy-now-btn').forEach(button => {
    button.addEventListener('click', async (e) => {
        const service = e.target.getAttribute('data-service');
        const price = parseInt(e.target.getAttribute('data-price'));
        
        // Disable button during processing
        e.target.disabled = true;
        e.target.textContent = 'Processing...';
        
        try {
            // In a real application, you would call your backend to create a payment session
            // For demo purposes, we'll simulate the Stripe checkout process
            await simulateStripeCheckout(service, price);
        } catch (error) {
            console.error('Payment error:', error);
            alert('Payment processing failed. Please try again.');
        } finally {
            // Re-enable button
            e.target.disabled = false;
            e.target.textContent = 'Buy Now';
        }
    });
});

// Simulate Stripe Checkout (replace with actual Stripe integration)
async function simulateStripeCheckout(service, price) {
    // In a real application, you would:
    // 1. Call your backend to create a Stripe checkout session
    // 2. Redirect to Stripe checkout
    // 3. Handle success/cancel redirects
    
    // For demo purposes, we'll show an alert
    const serviceName = service === 'digital-marketing' ? 'Digital Marketing Package' : 'Website Development';
    const priceFormatted = (price / 100).toFixed(2);
    
    const confirmed = confirm(`Proceed to payment for ${serviceName} - $${priceFormatted}?\n\nNote: This is a demo. In a real application, you would be redirected to Stripe checkout.`);
    
    if (confirmed) {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        alert('Payment successful! (Demo mode)\n\nYou will receive a confirmation email shortly.');
    }
}

// Quote Request Button Functionality
document.querySelectorAll('.quote-btn').forEach(button => {
    button.addEventListener('click', (e) => {
        const service = e.target.getAttribute('data-service');
        const serviceName = getServiceName(service);
        
        serviceTypeInput.value = service;
        document.querySelector('#quoteModal h2').textContent = `Request a Quote - ${serviceName}`;
        quoteModal.style.display = 'block';
        
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('preferredDate').setAttribute('min', today);
    });
});

// Get Service Name Helper Function
function getServiceName(service) {
    const serviceNames = {
        'business-consulting': 'Business Consulting',
        'it-support': 'IT Support & Maintenance'
    };
    return serviceNames[service] || 'Service';
}

// Close Modal Functionality
closeModal.addEventListener('click', () => {
    quoteModal.style.display = 'none';
});

window.addEventListener('click', (e) => {
    if (e.target === quoteModal) {
        quoteModal.style.display = 'none';
    }
});

// Quote Form Submission
quoteForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(quoteForm);
    const quoteData = Object.fromEntries(formData);
    
    // Disable submit button during processing
    const submitBtn = quoteForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = 'Submitting...';
    
    try {
        // Simulate form submission (replace with actual backend call)
        await simulateQuoteSubmission(quoteData);
        
        // Show success message
        alert('Quote request submitted successfully!\n\nWe will contact you within 24 hours to schedule your visit.');
        
        // Reset form and close modal
        quoteForm.reset();
        quoteModal.style.display = 'none';
        
    } catch (error) {
        console.error('Quote submission error:', error);
        alert('Failed to submit quote request. Please try again.');
    } finally {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
});

// Simulate Quote Submission (replace with actual backend integration)
async function simulateQuoteSubmission(quoteData) {
    // In a real application, you would:
    // 1. Send data to your backend
    // 2. Store in database
    // 3. Send confirmation email
    // 4. Notify your team
    
    console.log('Quote request data:', quoteData);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate successful submission
    return { success: true, id: Date.now() };
}

// Form Validation Enhancement
function validateForm() {
    const requiredFields = quoteForm.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#e74c3c';
            isValid = false;
        } else {
            field.style.borderColor = '#e1e8ed';
        }
    });
    
    return isValid;
}

// Add real-time validation
quoteForm.querySelectorAll('input, textarea, select').forEach(field => {
    field.addEventListener('blur', () => {
        if (field.hasAttribute('required') && !field.value.trim()) {
            field.style.borderColor = '#e74c3c';
        } else {
            field.style.borderColor = '#e1e8ed';
        }
    });
    
    field.addEventListener('input', () => {
        if (field.style.borderColor === 'rgb(231, 76, 60)' && field.value.trim()) {
            field.style.borderColor = '#e1e8ed';
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.background = '#fff';
        header.style.backdropFilter = 'none';
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    console.log('ProServices website loaded successfully!');
    
    // Set current year in footer if needed
    const currentYear = new Date().getFullYear();
    const footerText = document.querySelector('.footer-bottom p');
    if (footerText) {
        footerText.textContent = footerText.textContent.replace('2024', currentYear);
    }
});
