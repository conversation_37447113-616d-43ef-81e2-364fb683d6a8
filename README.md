# ProServices - Professional Service Website

A modern, responsive service website built with HTML, CSS, and JavaScript (no frameworks). Features online payments via Stripe and quote request functionality.

## Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Professional Layout**: Clean, modern design with proper typography and spacing
- **Service Showcase**: 4 services displayed in an attractive card layout
- **Online Payments**: Stripe integration for 2 services (Digital Marketing & Web Development)
- **Quote Requests**: Modal form for custom services (Business Consulting & IT Support)
- **Terms & Conditions**: Separate page with comprehensive legal terms
- **Mobile Navigation**: Hamburger menu for mobile devices
- **Smooth Scrolling**: Enhanced user experience with smooth page navigation

## Services Included

### Buy Now Services (Stripe Integration)
1. **Digital Marketing Package** - $299/month
2. **Website Development** - $1,499

### Quote Request Services
3. **Business Consulting** - Custom pricing
4. **IT Support & Maintenance** - Custom pricing

## File Structure

```
├── index.html          # Main homepage
├── terms.html          # Terms and Conditions page
├── styles.css          # All CSS styles
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Setup Instructions

### 1. Basic Setup
1. Download all files to your web server directory
2. Open `index.html` in a web browser
3. The website should work immediately for demo purposes

### 2. Stripe Integration Setup
To enable real payments, you need to:

1. **Create a Stripe Account**:
   - Go to [stripe.com](https://stripe.com) and create an account
   - Get your publishable key from the Stripe dashboard

2. **Update JavaScript**:
   - Open `script.js`
   - Replace `'pk_test_51234567890abcdef'` with your actual Stripe publishable key
   - Line 2: `const stripe = Stripe('your_actual_publishable_key_here');`

3. **Backend Integration** (Required for production):
   - The current implementation is demo-only
   - You need to create a backend endpoint to handle Stripe checkout sessions
   - Replace the `simulateStripeCheckout` function with actual Stripe API calls
   - Recommended: Use Stripe's official documentation for your backend language

### 3. Quote Form Backend Integration
To handle quote requests in production:

1. **Create Backend Endpoint**:
   - Set up an endpoint to receive form submissions
   - Store quote requests in a database
   - Send confirmation emails to customers
   - Notify your team of new requests

2. **Update JavaScript**:
   - Replace the `simulateQuoteSubmission` function
   - Point to your actual backend endpoint
   - Add proper error handling

## Customization

### Colors
The website uses a blue color scheme. To change colors, update these CSS variables in `styles.css`:
- Primary blue: `#3498db`
- Dark blue: `#2c3e50`
- Gradient: `#667eea` to `#764ba2`

### Content
- Update company name in `index.html` (currently "ProServices")
- Modify service descriptions, prices, and icons
- Update contact information in the footer and contact section
- Customize the Terms & Conditions page

### Services
To add/remove services:
1. Update the service cards in `index.html`
2. Add corresponding JavaScript handlers in `script.js`
3. Update the service name mapping in the `getServiceName` function

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Security Notes

- The current Stripe integration is for demo purposes only
- Never expose your Stripe secret key in frontend code
- Always validate form data on the backend
- Implement proper CSRF protection for forms
- Use HTTPS in production

## Performance

- No external dependencies except Stripe.js
- Optimized CSS with minimal animations
- Responsive images and layouts
- Fast loading times

## License

This is a template/demo website. Feel free to use and modify for your business needs.

## Support

For questions about implementation or customization, please refer to:
- [Stripe Documentation](https://stripe.com/docs)
- [MDN Web Docs](https://developer.mozilla.org/) for HTML/CSS/JS reference

---

**Note**: Remember to replace the demo Stripe key and implement proper backend integration before using in production!
